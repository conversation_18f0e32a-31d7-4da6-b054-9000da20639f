body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON>o, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    margin: 0;
    padding: 20px;
    color: #2d3748;
    position: relative;
}

/* 保存功能样式 */
.save-controls {
    position: fixed;
    top: 20px;
    right: 20px;
    display: flex;
    gap: 10px;
    z-index: 1000;
}

.save-btn, .load-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: #4a5568;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.save-btn:hover, .load-btn:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
}

.save-btn svg, .load-btn svg {
    color: #667eea;
}

.save-btn:active, .load-btn:active {
    transform: translateY(0);
}

#journey-map {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.area {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 32px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.area:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.area h2 {
    margin-top: 0;
    font-size: 1.6em;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    border-bottom: 2px solid rgba(102, 126, 234, 0.2);
    padding-bottom: 16px;
    margin-bottom: 28px;
}

/* Area A: User Model */
.area-a .user-model-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
}

.user-persona, .scenario, .goals {
    border: none;
    padding: 0;
    border-radius: 12px;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.user-persona:hover, .scenario:hover, .goals:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.12);
}

.non-editable {
    pointer-events: none;
    user-select: none;
    background: transparent;
    margin: 0;
    padding: 16px 24px;
    font-size: 1em;
    font-weight: 600;
    color: #4a5568;
    border-bottom: 1px solid rgba(102, 126, 234, 0.15);
}

.editable-content {
    padding: 24px;
    min-height: 80px;
    background: rgba(255, 255, 255, 0.8);
}

.editable-content p {
    color: #4a5568;
    font-style: italic;
    line-height: 1.7;
    margin: 0;
    font-size: 0.95em;
}

.user-persona .editable-content p:empty::before {
    content: "描述目标用户的基本信息，包括年龄、职业、技能水平、使用习惯等关键特征...";
}

.scenario .editable-content p:empty::before {
    content: "描述用户使用产品的具体场景，包括时间、地点、环境、触发因素等背景信息...";
}

.goals .editable-content p:empty::before {
    content: "明确用户想要达成的目标，包括功能性需求、情感性需求和期望的结果...";
}

.user-persona .editable-content p:empty::before,
.scenario .editable-content p:empty::before,
.goals .editable-content p:empty::before {
    color: #cbd5e0;
    font-style: italic;
}

[contenteditable="true"]:focus {
    outline: 2px solid #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.2);
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 6px;
}

/* Area B: Journey Stages */
.journey-stages-container {
    display: flex;
    gap: 32px;
    overflow-x: auto;
    padding-bottom: 0;
    margin-bottom: 0;
}

.journey-stage {
    position: relative;
    flex: 0 0 320px;
    display: flex;
    flex-direction: column;
    gap: 14px;
    background: linear-gradient(145deg, #ffffff 0%, #f7fafc 100%);
    padding: 20px;
    border-radius: 16px;
    border: none;
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.08);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    z-index: 1;
}

.journey-stage:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.12);
    cursor: grab;
}

.journey-stage:active {
    cursor: grabbing;
}

.stage-placeholder {
    flex: 0 0 320px;
    background: rgba(102, 126, 234, 0.2) !important;
    border: 2px dashed #667eea !important;
    border-radius: 16px !important;
}

.stage-header {
    font-weight: 700;
    font-size: 1.15em;
    padding: 12px 16px;
    text-align: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    border-bottom: 2px solid rgba(102, 126, 234, 0.15);
    margin-bottom: 12px;
    border-radius: 8px 8px 0 0;
}

/* 情感值输入区域 */
.sentiment-input-container {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border: 1px solid rgba(102, 126, 234, 0.2);
    border-radius: 10px;
    margin-bottom: 12px;
    transition: all 0.2s ease;
}

.sentiment-input-container:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%);
    border-color: rgba(102, 126, 234, 0.3);
}

.sentiment-label {
    font-size: 0.9em;
    font-weight: 600;
    color: #667eea;
    min-width: 55px;
}

.sentiment-input {
    flex: 1;
    border: none;
    outline: none;
    font-size: 0.9em;
    padding: 4px;
    text-align: center;
    font-weight: 600;
}

.sentiment-indicator {
    font-size: 1.2em;
    min-width: 24px;
    text-align: center;
}

.lane {
    border: none;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.7);
    padding: 12px;
    min-height: 85px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.lane:hover {
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.lane-title {
    font-size: 0.85em;
    font-weight: 600;
    color: #667eea;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.item {
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    padding: 12px;
    border-radius: 8px;
    border: 1px solid rgba(102, 126, 234, 0.1);
    margin-bottom: 8px;
    cursor: text;
    font-size: 0.9em;
    line-height: 1.5;
    color: #4a5568;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.item:hover {
    background: linear-gradient(145deg, #f8fafc 0%, #edf2f7 100%);
    border-color: rgba(102, 126, 234, 0.2);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.add-stage-button-container {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 0 0 150px;
}

.add-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 14px 24px;
    cursor: pointer;
    font-size: 1em;
    font-weight: 600;
    transition: all 0.2s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.add-btn:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* Area C: Sentiment Curve */
.area-c {
    /* 移除额外的margin-top，使用容器的gap保持一致间距 */
}

.sentiment-curve {
    padding: 0;
    background: transparent;
    border: none;
    border-radius: 0;
    box-shadow: none;
}

/* 移除 row-title 样式，现在使用统一的 h2 样式 */

.chart-description {
    margin-bottom: 16px;
}

.chart-description p {
    color: #718096;
    font-size: 0.95em;
    margin: 0;
    font-style: italic;
}

#chart {
    margin-top: 16px;
    margin-bottom: 0px;
}

/* 心情曲线下方痛点/机会区域样式 */
.chart-opportunities-section {
    margin-top: 0px;
    padding-top: 0;
    position: relative;
}

.chart-opportunities-buttons {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    gap: 8px;
    margin-bottom: 20px;
    flex-wrap: nowrap;
    padding: 0 20px;
    position: relative;
    min-height: 50px;
    overflow: visible;
}

.chart-stage-column {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    min-width: 120px;
    max-width: 200px;
    position: relative;
    margin-bottom: 0;
    height: 100%;
}

.chart-stage-column:first-child {
    align-items: flex-start;
}

.chart-stage-column:last-child {
    align-items: flex-end;
}

/* 每个阶段的痛点卡片容器 */
.chart-stage-opportunities {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 12px;
    width: 100%;
    max-width: 200px;
    min-height: 0;
    z-index: 1;
    position: relative;
    /* 移除 flex-grow: 1 以避免高度不一致 */
    /* 确保按照DOM顺序显示 */
    order: 0;
}

.opportunity-card {
    background: linear-gradient(145deg, #f0f8ff 0%, #e6f3ff 100%);
    border: none;
    border-radius: 12px;
    padding: 0;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.1);
    transition: all 0.3s ease;
    position: relative;
    max-width: 200px;
    width: 100%;
    margin-bottom: 4px;
    z-index: 2;
    min-height: 120px;
    display: flex;
    flex-direction: column;
}

.opportunity-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
    border-color: rgba(59, 130, 246, 0.5);
}

/* 点击时置于顶层 */
.opportunity-card.top-layer {
    z-index: 99999 !important;
    transform: translateY(-20px) scale(1.05) !important;
    box-shadow: 0 20px 40px rgba(59, 130, 246, 0.5) !important;
    border: 2px solid rgba(59, 130, 246, 0.8) !important;
    background: linear-gradient(145deg, #f8fbff 0%, #f0f7ff 100%) !important;
    position: relative !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 包含置顶卡片的阶段容器 */
.journey-stage.stage-top-layer {
    z-index: 9998 !important;
}

.opportunity-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 4px;
    padding: 8px 8px 0 8px;
}

.opportunity-title {
    font-size: 0.9em;
    font-weight: 700;
    color: #3b82f6;
    display: flex;
    align-items: center;
    gap: 8px;
}

.opportunity-content {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 8px;
    padding: 10px;
    min-height: 60px;
    font-size: 0.85em;
    line-height: 1.5;
    color: #4a5568;
    outline: none;
    transition: all 0.2s ease;
    word-wrap: break-word;
    overflow-wrap: break-word;
    white-space: pre-wrap;
    flex: 1;
    margin: 0 8px 8px 8px;
}

.opportunity-content:focus {
    border-color: rgba(59, 130, 246, 0.5);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.chart-opportunity-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 3px;
    background: transparent;
    color: #3b82f6;
    border: 2px solid #3b82f6;
    border-radius: 6px;
    padding: 6px 8px;
    font-size: 0.7em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: none;
    width: 100%;
    max-width: 100px;
    min-height: 28px;
    white-space: nowrap;
}

.chart-opportunity-btn:hover {
    background: rgba(59, 130, 246, 0.1);
    border-color: #2563eb;
    color: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.chart-opportunity-btn:active {
    transform: translateY(0);
}

.chart-opportunity-btn .btn-icon {
    font-size: 1em;
    flex-shrink: 0;
}

.chart-opportunity-btn .btn-text {
    font-size: 0.9em;
    flex-shrink: 0;
}

.btn-icon {
    font-size: 1.2em;
}

.delete-opportunity-btn {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(220, 53, 69, 0.2);
    border-radius: 50%;
    width: 28px;
    height: 28px;
    font-size: 16px;
    cursor: pointer;
    color: #e53e3e;
    padding: 0;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.delete-opportunity-btn:hover {
    background: #e53e3e;
    color: white;
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(229, 62, 62, 0.3);
}

.delete-stage-btn {
    position: absolute;
    top: 12px;
    right: 12px;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(220, 53, 69, 0.2);
    border-radius: 50%;
    width: 28px;
    height: 28px;
    font-size: 16px;
    cursor: pointer;
    color: #e53e3e;
    padding: 0;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.delete-stage-btn:hover {
    background: #e53e3e;
    color: white;
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(229, 62, 62, 0.3);
}

/* 自定义确认对话框 */
.custom-confirm-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.custom-confirm-overlay.show {
    opacity: 1;
    visibility: visible;
}

.custom-confirm-dialog {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 32px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 400px;
    width: 90%;
    text-align: center;
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.custom-confirm-overlay.show .custom-confirm-dialog {
    transform: scale(1);
}

.custom-confirm-title {
    font-size: 1.4em;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 16px;
}

.custom-confirm-message {
    color: #4a5568;
    font-size: 1em;
    line-height: 1.6;
    margin-bottom: 24px;
}

.custom-confirm-buttons {
    display: flex;
    gap: 12px;
    justify-content: center;
}

.custom-confirm-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 12px;
    font-size: 1em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 100px;
}

.custom-confirm-btn.confirm {
    background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(229, 62, 62, 0.3);
}

.custom-confirm-btn.confirm:hover {
    background: linear-gradient(135deg, #c53030 0%, #9c2626 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(229, 62, 62, 0.4);
}

.custom-confirm-btn.cancel {
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    color: #4a5568;
    border: 1px solid rgba(102, 126, 234, 0.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.custom-confirm-btn.cancel:hover {
    background: linear-gradient(145deg, #f8fafc 0%, #edf2f7 100%);
    border-color: rgba(102, 126, 234, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Footer样式 */
.footer {
    margin-top: 60px;
    padding: 20px 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    text-align: center;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.footer p {
    margin: 0;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9em;
    font-weight: 500;
    letter-spacing: 0.5px;
}
